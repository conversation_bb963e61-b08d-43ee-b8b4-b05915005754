CREATE TABLE kullanici_grup_yetkiler
(
    id                BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    is_deleted        BOOLEA<PERSON>,
    created_at        TIMESTAMP WITHOUT TIME ZONE,
    updated_at        TIMESTAMP WITHOUT TIME ZONE,
    deleted_at        TIMESTAMP WITHOUT TIME ZONE,
    created_by        <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by        <PERSON><PERSON><PERSON><PERSON>(255),
    deleted_by        VARC<PERSON><PERSON>(255),
    kullanici_grup_id BIGINT                                  NOT NULL,
    yetki_id          BIGINT                                  NOT NULL,
    CONSTRAINT pk_kullanici_grup_yetkiler PRIMARY KEY (id)
);

CREATE TABLE kullanici_gruplar
(
    id         BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    is_deleted BOOLEAN,
    created_at TIMESTAMP WITHOUT TIME ZONE,
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    deleted_at TIMESTAMP WITHOUT TIME ZONE,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by <PERSON><PERSON><PERSON><PERSON>(255),
    deleted_by <PERSON><PERSON><PERSON><PERSON>(255),
    ad         VARCHAR(255)                            NOT NULL,
    CONSTRAINT pk_kullanici_gruplar PRIMARY KEY (id)
);

ALTER TABLE kullanici_grup_yetkiler
    ADD CONSTRAINT FK_KULLANICI_GRUP_YETKILER_ON_KULLANICI_GRUP FOREIGN KEY (kullanici_grup_id) REFERENCES kullanici_gruplar (id);

ALTER TABLE kullanici_grup_yetkiler
    ADD CONSTRAINT FK_KULLANICI_GRUP_YETKILER_ON_YETKI FOREIGN KEY (yetki_id) REFERENCES yetkiler (id);