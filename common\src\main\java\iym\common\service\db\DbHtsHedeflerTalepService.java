package iym.common.service.db;

import iym.common.model.entity.iym.HtsHedeflerTalep;

import java.util.Date;
import java.util.List;

/**
 * Service interface for HtsHedeflerTalep entity
 */
public interface DbHtsHedeflerTalepService extends GenericDbService<HtsHedeflerTalep, Long> {

    List<HtsHedeflerTalep> findByMahkemeKararId(Long mahkemeKararId);
    
    List<HtsHedeflerTalep> findByHedefNo(String hedefNo);
    
    List<HtsHedeflerTalep> findByHedefNoAndMahkemeKararId(String hedefNo, Long mahkemeKararId);
    
    List<HtsHedeflerTalep> findByKarsiHedefNo(String karsiHedefNo);
    
    List<HtsHedeflerTalep> findByHedefNoAndKarsiHedefNo(String hedefNo, String karsiHedefNo);
    
    List<HtsHedeflerTalep> findBySorguTipi(String sorguTipi);
    
    List<HtsHedeflerTalep> findByTespitTuru(String tespitTuru);
    
    List<HtsHedeflerTalep> findByKullaniciId(String kullaniciId);
    
    List<HtsHedeflerTalep> findByDurumu(String durumu);
    
    List<HtsHedeflerTalep> findByBaslangicTarihiBetween(Date startDate, Date endDate);
    
    List<HtsHedeflerTalep> findByBitisTarihiBetween(Date startDate, Date endDate);
    
    List<HtsHedeflerTalep> findByBaslangicTarihiLessThanEqualAndBitisTarihiGreaterThanEqual(Date date1, Date date2);
    
    List<HtsHedeflerTalep> findByHedefNoContainingIgnoreCase(String hedefNo);
    
    List<HtsHedeflerTalep> findByKarsiHedefNoContainingIgnoreCase(String karsiHedefNo);
    
    List<HtsHedeflerTalep> findBySorguTipiAndTespitTuru(String sorguTipi, String tespitTuru);
    
    List<HtsHedeflerTalep> findByMahkemeKararIdAndDurumu(Long mahkemeKararId, String durumu);
}
