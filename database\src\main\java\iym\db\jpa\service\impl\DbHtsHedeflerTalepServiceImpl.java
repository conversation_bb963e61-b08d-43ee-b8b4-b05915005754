package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.HtsHedeflerTalep;
import iym.common.service.db.DbHtsHedeflerTalepService;
import iym.db.jpa.dao.HtsHedeflerTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * Service implementation for HtsHedeflerTalep entity
 */
@Service
public class DbHtsHedeflerTalepServiceImpl extends GenericDbServiceImpl<HtsHedeflerTalep, Long> implements DbHtsHedeflerTalepService {

    private final HtsHedeflerTalepRepo htsHedeflerTalepRepo;

    @Autowired
    public DbHtsHedeflerTalepServiceImpl(HtsHedeflerTalepRepo repository) {
        super(repository);
        this.htsHedeflerTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findByMahkemeKararId(Long mahkemeKararId) {
        return htsHedeflerTalepRepo.findByMahkemeKararId(mahkemeKararId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findByHedefNo(String hedefNo) {
        return htsHedeflerTalepRepo.findByHedefNo(hedefNo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findByHedefNoAndMahkemeKararId(String hedefNo, Long mahkemeKararId) {
        return htsHedeflerTalepRepo.findByHedefNoAndMahkemeKararId(hedefNo, mahkemeKararId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findByKarsiHedefNo(String karsiHedefNo) {
        return htsHedeflerTalepRepo.findByKarsiHedefNo(karsiHedefNo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findByHedefNoAndKarsiHedefNo(String hedefNo, String karsiHedefNo) {
        return htsHedeflerTalepRepo.findByHedefNoAndKarsiHedefNo(hedefNo, karsiHedefNo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findBySorguTipi(String sorguTipi) {
        return htsHedeflerTalepRepo.findBySorguTipi(sorguTipi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findByTespitTuru(String tespitTuru) {
        return htsHedeflerTalepRepo.findByTespitTuru(tespitTuru);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findByKullaniciId(String kullaniciId) {
        return htsHedeflerTalepRepo.findByKullaniciId(kullaniciId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findByDurumu(String durumu) {
        return htsHedeflerTalepRepo.findByDurumu(durumu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findByBaslangicTarihiBetween(Date startDate, Date endDate) {
        return htsHedeflerTalepRepo.findByBaslangicTarihiBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findByBitisTarihiBetween(Date startDate, Date endDate) {
        return htsHedeflerTalepRepo.findByBitisTarihiBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findByBaslangicTarihiLessThanEqualAndBitisTarihiGreaterThanEqual(Date date1, Date date2) {
        return htsHedeflerTalepRepo.findByBaslangicTarihiLessThanEqualAndBitisTarihiGreaterThanEqual(date1, date2);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findByHedefNoContainingIgnoreCase(String hedefNo) {
        return htsHedeflerTalepRepo.findByHedefNoContainingIgnoreCase(hedefNo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findByKarsiHedefNoContainingIgnoreCase(String karsiHedefNo) {
        return htsHedeflerTalepRepo.findByKarsiHedefNoContainingIgnoreCase(karsiHedefNo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findBySorguTipiAndTespitTuru(String sorguTipi, String tespitTuru) {
        return htsHedeflerTalepRepo.findBySorguTipiAndTespitTuru(sorguTipi, tespitTuru);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findByMahkemeKararIdAndDurumu(Long mahkemeKararId, String durumu) {
        return htsHedeflerTalepRepo.findByMahkemeKararIdAndDurumu(mahkemeKararId, durumu);
    }
}
