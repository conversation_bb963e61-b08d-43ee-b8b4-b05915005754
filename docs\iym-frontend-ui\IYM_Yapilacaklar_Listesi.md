# IYM Yapılacaklar Listesi

## Proje <PERSON>
- Başlangıç: 29 Haziran 2025
- Durum: Tamamlandı
- Teknoloji: Angular 17, PrimeNG
- URL: http://localhost:4201/

## Tamamlanan İşler

### Analiz
- [x] Ekran görüntüleri incelendi
- [x] Menü yapısı çıkarıldı
- [x] XML dosyaları analiz edildi
- [x] Mevcut proje yapısı incelendi

### Geliştirme
- [x] Menü öğeleri eklendi
- [x] Dinleme evrak araması sayfası
- [x] Evrak gönderme sayfası
- [x] XML sorgulama sayfası
- [x] Parametre sorgulama sayfası
- [x] İletişimin tespiti sayfası

### Test
- [x] Sayfalar test edildi
- [x] Hatalar düzeltildi
- [x] Responsive tasarım kontrol edildi

## Sayfa Özellikleri

### Dinleme Evrak Araması
- <PERSON><PERSON><PERSON> numa<PERSON> arama
- <PERSON><PERSON>h seçici
- Mahkeme dropdown
- <PERSON><PERSON><PERSON> tablosu
- Export özelliği

### Evrak Gönderme
- Dosya yükleme
- XML validasyon
- İlerleme çubuğu
- Hata mesajları

### XML Sorgulama
- XML editor
- Syntax highlighting
- Validasyon
- Örnek şablonlar

### Parametre Sorgulama
- Parametre listesi
- Filtreleme
- Düzenleme

### İletişimin Tespiti
- Tespit listesi
- Detay görüntüleme
- Durum takibi

## Teknik Bilgiler

### Dosya Yapısı
```
src/app/iym/
├── dinleme-evrak-aramasi/
├── evrak-gonderme/
├── xml-sorgulama/
├── parametre-sorgulama/
├── iletisimin-tespiti/
└── shared/
```

### Kullanılan Teknolojiler
- Angular 17
- PrimeNG
- TypeScript
- SCSS

### Erişim
- URL: http://localhost:4201/
- Menü: Ana Menü → IYM
