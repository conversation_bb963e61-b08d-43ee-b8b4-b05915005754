// IYM (İ<PERSON><PERSON><PERSON><PERSON><PERSON>) Model Tanımları

export interface EvrakKayit {
  xmlVersiyon: string;
  evrakNo: string;
  evrakTarihi: string;
  evrakGeldigiKurum: string;
  evrakTipi: 'ILETISIMIN_DENETLENMESI' | 'ILETISIMIN_TESPITI';
  havaleBirim: string;
  aciklama?: string;
  gelIl: string;
  evrakKonusu?: string;
  acilmi: 'H' | 'E'; // Hayır/Evet
  mahkemeKarar: MahkemeKarar;
}

export interface MahkemeKarar {
  kararTip: string;
  mahkemeKodu: string;
  mahkemeKararNo: string;
  mahkemeIli: string;
  aciklama?: string;
  sorusturmaNo?: string;
  hedefler?: Hedef[];
  itkHedefler?: ItkHedef[];
  mahkemeAidiyat?: MahkemeAidiyat[];
  mahkemeSucTipi?: MahkemeSucTipi[];
}

export interface Hedef {
  hedefNo: string;
  hedefTipi: string;
  hedefAdi?: string;
  hedefSoyadi?: string;
  baslamaTarihi: string;
  suresi?: string;
  sureTipi: string;
  bimAidiyatKod: string;
  canakNo?: string;
}

export interface ItkHedef {
  sorguTipi: string;
  hedefNo: string;
  karsiHedefNo?: string;
  baslangicTarihi: string;
  bitisTarihi: string;
  tespitTuru: string;
  tespitTuruDetay?: string;
  aciklama?: string;
}

export interface MahkemeAidiyat {
  aidiyatKod: string;
}

export interface MahkemeSucTipi {
  sucTipKod: string;
}

// Karar Tipleri Enum
export enum KararTipleri {
  ONLEYICI_HAKIM_KARARI = '100',
  ONLEYICI_YAZILI_EMIR = '200',
  ADLI_HAKIM_KARARI = '300',
  ADLI_YAZILI_EMIR = '400',
  ADLI_KHK_YAZILI_EMIR = '410',
  BILGI_ALMA = '500',
  ONLEYICI_SONLANDIRMA = '600',
  ADLI_SONLANDIRMA = '700',
  ADLI_KHK_SONLANDIRMA = '730',
  ADLI_ASKERI_HAKIM_KARARI = '800',
  ADLI_ASKERI_SONLANDIRMA = '900',
  ONLEYICI_HAKIM_T_KARAR = '150'
}

// Hedef Tipleri Enum
export enum HedefTipleri {
  GSM = '10',
  SABIT = '20',
  UYDU = '30',
  YURT_DISI = '40',
  UMTH_MSISDN = '41',
  UMTH_USERNAME = '42',
  UMTH_IP = '43',
  UMTH_PINCODE = '44',
  EPOSTA = '50',
  IP_TAKIP = '51',
  URL_WEB_ADRESI_TAKIP = '52',
  ADSL_ABONE_TAKIP = '53',
  GPRS = '54',
  IP_ENGELLEME = '55',
  DOMAIN_ENGELLEME = '56',
  IMEI = '60',
  IMSI = '70',
  GPRS_IMSI = '71',
  TT_XDSL_MSISDN = '80',
  TT_XDSL_TEMOSNO = '81',
  TT_XDSL_USERNAME = '82',
  TT_XDSL_IP = '83',
  GPRS_GSM = '90',
  GPRS_IMEI = '91',
  GPRS_YURT_DISI = '92',
  GSM_YER_TESPITI = '200'
}

// Arama Filtreleri
export interface EvrakAramaFiltresi {
  evrakNo?: string;
  baslangicTarihi?: Date;
  bitisTarihi?: Date;
  mahkemeKodu?: string;
  evrakTipi?: string;
  kararTipi?: string;
  hedefNo?: string;
}

export interface ParametreAramaFiltresi {
  parametreAdi?: string;
  parametreGrubu?: string;
  aktifMi?: boolean;
}

// Sonuç Modelleri
export interface EvrakAramaSonucu {
  evrakNo: string;
  evrakTarihi: string;
  evrakTipi: string;
  mahkemeKodu: string;
  kararTipi: string;
  durumu: string;
  aciklama?: string;
}

export interface ParametreSonucu {
  id: number;
  parametreAdi: string;
  parametreDegeri: string;
  parametreGrubu: string;
  aciklama?: string;
  aktifMi: boolean;
  olusturmaTarihi: Date;
  guncellemeTarihi?: Date;
}

// XML İşlem Sonuçları
export interface XmlValidasyonSonucu {
  gecerliMi: boolean;
  hatalar: XmlHata[];
  uyarilar: XmlUyari[];
}

export interface XmlHata {
  satir: number;
  sutun: number;
  mesaj: string;
  seviye: 'ERROR' | 'WARNING' | 'INFO';
}

export interface XmlUyari {
  satir: number;
  sutun: number;
  mesaj: string;
  oneri?: string;
}

// Dosya Yükleme
export interface DosyaYuklemeResponse {
  basarili: boolean;
  dosyaAdi: string;
  dosyaBoyutu: number;
  yuklenmeTarihi: Date;
  hataMesaji?: string;
  validasyonSonucu?: XmlValidasyonSonucu;
}

// Sistem Parametreleri
export interface SistemParametresi {
  id: number;
  anahtar: string;
  deger: string;
  aciklama?: string;
  kategori: string;
  veriTipi: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE';
  zorunluMu: boolean;
  varsayilanDeger?: string;
}

// İstatistik Modelleri
export interface IymIstatistik {
  toplamEvrak: number;
  bekleyenEvrak: number;
  islenenEvrak: number;
  hatalıEvrak: number;
  bugunIslemler: number;
  sonIslemTarihi?: Date;
}

// Rapor Modelleri
export interface RaporParametresi {
  raporTipi: 'GUNLUK' | 'HAFTALIK' | 'AYLIK' | 'OZEL';
  baslangicTarihi: Date;
  bitisTarihi: Date;
  mahkemeKodu?: string;
  evrakTipi?: string;
  formatTipi: 'PDF' | 'EXCEL' | 'CSV';
}

export interface RaporSonucu {
  raporId: string;
  raporAdi: string;
  olusturmaTarihi: Date;
  dosyaYolu: string;
  dosyaBoyutu: number;
  indirmeLinki: string;
}

// İletişim tespiti modelleri
export interface IletisimTespitiFiltresi {
  evrakNo?: string;
  hedefBilgisi?: string;
  tespitiTuru?: string;
  durum?: string;
  baslangicTarihi?: Date;
  bitisTarihi?: Date;
}

export interface IletisimTespitiSonucu {
  id: number;
  evrakNo: string;
  mahkemeKodu: string;
  hedefBilgisi: string;
  tespitiTuru: string;
  durum: string;
  talepTarihi: Date;
  tamamlanmaTarihi?: Date;
  sonucBilgisi?: string;
  hataMesaji?: string;
  ilerlemeYuzdesi: number;
}
