package iym.common.model.entity.makos;

import com.fasterxml.jackson.annotation.JsonIgnore;
import iym.common.model.api.KullaniciKurum;
import iym.common.model.enums.MakosUserRoleType;
import iym.common.model.enums.UserStatusType;
import iym.common.util.db.KullaniciKurumConverter;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;


@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MakosUser")
@Table(name = "MAKOS_USER", uniqueConstraints = { @UniqueConstraint(name = "MAKOS_USER_UN", columnNames = { "USERNAME"}) })
public class MakosUser implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAKOS_USER_SEQ")
    @SequenceGenerator(name = "MAKOS_USER_SEQ", sequenceName = "MAKOS_USER_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "USERNAME")
    @NotNull
    @NotBlank
    @Size(min = 4, max = 100)
    private String username;

    @Column(name = "PASSWORD")
    @NotNull
    @NotBlank
    @Size(min = 5, max = 100)
    @JsonIgnore
    private String password;

    @Column(name = "STATUS")
    @NotNull
    @Enumerated(EnumType.STRING)
    private UserStatusType status;

    @Column(name = "ROLE")
    @NotNull
    @Enumerated(EnumType.STRING)
    private MakosUserRoleType role;

    @Column(name = "KURUM")
    @Convert(converter = KullaniciKurumConverter.class)
    private KullaniciKurum kurum;

    @Transient
    private String newPassword;

    @Transient
    @JsonIgnore
    public boolean isActive() {
        return getStatus() == UserStatusType.ACTIVE;
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", status=" + status +
                '}';
    }

}
