package tr.gov.btk.menuitem.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import tr.gov.btk.menuitem.entity.MenuItem;
import tr.gov.btk.shared.repository.BaseRepository;

import java.util.List;

public interface MenuItemRepository extends BaseRepository<MenuItem, Long> {
    @Query("SELECT m FROM MenuItem m " +
            "LEFT JOIN FETCH m.menuItemYetkiler miy " +
            "LEFT JOIN FETCH miy.yetki " +
            "LEFT JOIN FETCH m.parent")
    List<MenuItem> findAllWithYetkilerAndParent();
}
