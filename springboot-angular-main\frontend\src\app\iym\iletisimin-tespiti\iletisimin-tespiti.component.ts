import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

// PrimeNG Imports
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { ToastModule } from 'primeng/toast';
import { DialogModule } from 'primeng/dialog';
import { CardModule } from 'primeng/card';
import { TagModule } from 'primeng/tag';
import { ProgressBarModule } from 'primeng/progressbar';
import { DividerModule } from 'primeng/divider';
import { MessagesModule } from 'primeng/messages';
import { MessageModule } from 'primeng/message';
import { ToolbarModule } from 'primeng/toolbar';
import { TabViewModule } from 'primeng/tabview';

import { MessageService } from 'primeng/api';

// IYM Imports
import { IymService } from '../shared/services/iym.service';
import { IletisimTespitiFiltresi, IletisimTespitiSonucu } from '../shared/models/iym.models';

@Component({
  selector: 'app-iletisimin-tespiti',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    ButtonModule,
    InputTextModule,
    DropdownModule,
    CalendarModule,
    ToastModule,
    DialogModule,
    CardModule,
    TagModule,
    ProgressBarModule,
    DividerModule,
    MessagesModule,
    MessageModule,
    ToolbarModule,
    TabViewModule
  ],
  providers: [MessageService],
  templateUrl: './iletisimin-tespiti.component.html',
  styleUrls: ['./iletisimin-tespiti.component.scss']
})
export class IletisiminTespitiComponent implements OnInit {
  
  // Arama ve Filtreleme
  aramaFiltresi: IletisimTespitiFiltresi = {};
  sonuclar: IletisimTespitiSonucu[] = [];
  filtrelenmissonuclar: IletisimTespitiSonucu[] = [];
  
  // UI Durumları
  yukleniyor = false;
  detayDialogGoruntule = false;
  
  // Seçili kayıt
  seciliKayit: IletisimTespitiSonucu | null = null;
  
  // Dropdown Seçenekleri
  tespitiTurleri = [
    { label: 'Tümü', value: null },
    { label: 'GSM Abone Bilgisi', value: 'GSM_ABONE' },
    { label: 'Sabit Hat Abone Bilgisi', value: 'SABIT_HAT' },
    { label: 'İnternet Abone Bilgisi', value: 'INTERNET' },
    { label: 'IP Adresi Tespiti', value: 'IP_ADRESI' },
    { label: 'E-posta Tespiti', value: 'EPOSTA' }
  ];
  
  durumSecenekleri = [
    { label: 'Tümü', value: null },
    { label: 'Beklemede', value: 'BEKLEMEDE' },
    { label: 'İşlemde', value: 'ISLEMDE' },
    { label: 'Tamamlandı', value: 'TAMAMLANDI' },
    { label: 'Hata', value: 'HATA' }
  ];
  
  // Tarih aralığı
  baslangicTarihi: Date | null = null;
  bitisTarihi: Date | null = null;
  
  // Arama metni
  globalAramaMetni = '';
  
  // Tab indeksi
  aktifTabIndex = 0;

  constructor(
    private iymService: IymService,
    private messageService: MessageService
  ) {}

  ngOnInit() {
    this.varsayilanTarihleriAyarla();
    this.iletisimTespitleriniYukle();
  }

  getCurrentDateTime(): string {
    return new Date().toLocaleString('tr-TR');
  }

  varsayilanTarihleriAyarla() {
    // Son 30 gün
    this.bitisTarihi = new Date();
    this.baslangicTarihi = new Date();
    this.baslangicTarihi.setDate(this.baslangicTarihi.getDate() - 30);
    
    this.aramaFiltresi.baslangicTarihi = this.baslangicTarihi;
    this.aramaFiltresi.bitisTarihi = this.bitisTarihi;
  }

  iletisimTespitleriniYukle() {
    this.yukleniyor = true;
    
    this.iymService.iletisimTespitleriniGetir(this.aramaFiltresi).subscribe({
      next: (sonuclar) => {
        this.sonuclar = sonuclar;
        this.filtrelenmissonuclar = [...sonuclar];
        this.yukleniyor = false;
        
        this.messageService.add({
          severity: 'success',
          summary: 'Başarılı',
          detail: `${sonuclar.length} kayıt yüklendi`
        });
      },
      error: (hata) => {
        this.yukleniyor = false;
        this.messageService.add({
          severity: 'error',
          summary: 'Hata',
          detail: 'İletişim tespitleri yüklenemedi'
        });
      }
    });
  }

  aramaYap() {
    // Tarih kontrolü
    if (this.baslangicTarihi && this.bitisTarihi) {
      if (this.baslangicTarihi > this.bitisTarihi) {
        this.messageService.add({
          severity: 'warn',
          summary: 'Uyarı',
          detail: 'Başlangıç tarihi bitiş tarihinden büyük olamaz'
        });
        return;
      }
      
      // 90 günden fazla aralık kontrolü
      const gunFarki = Math.abs(this.bitisTarihi.getTime() - this.baslangicTarihi.getTime()) / (1000 * 60 * 60 * 24);
      if (gunFarki > 90) {
        this.messageService.add({
          severity: 'warn',
          summary: 'Uyarı',
          detail: 'Tarih aralığı 90 günden fazla olamaz'
        });
        return;
      }
    }
    
    this.aramaFiltresi.baslangicTarihi = this.baslangicTarihi || undefined;
    this.aramaFiltresi.bitisTarihi = this.bitisTarihi || undefined;
    
    this.iletisimTespitleriniYukle();
  }

  filtreleriTemizle() {
    this.aramaFiltresi = {};
    this.globalAramaMetni = '';
    this.varsayilanTarihleriAyarla();
    this.iletisimTespitleriniYukle();
  }

  globalArama() {
    if (!this.globalAramaMetni.trim()) {
      this.filtrelenmissonuclar = [...this.sonuclar];
      return;
    }
    
    const aramaMetni = this.globalAramaMetni.toLowerCase();
    this.filtrelenmissonuclar = this.sonuclar.filter(s => 
      s.evrakNo.toLowerCase().includes(aramaMetni) ||
      s.hedefBilgisi.toLowerCase().includes(aramaMetni) ||
      s.tespitiTuru.toLowerCase().includes(aramaMetni) ||
      s.mahkemeKodu.toLowerCase().includes(aramaMetni)
    );
  }

  detayGoster(kayit: IletisimTespitiSonucu) {
    this.seciliKayit = kayit;
    this.detayDialogGoruntule = true;
  }

  detayDialogKapat() {
    this.detayDialogGoruntule = false;
    this.seciliKayit = null;
  }

  excelAktar() {
    // Excel export işlemi - şimdilik mockup
    this.messageService.add({
      severity: 'info',
      summary: 'Bilgi',
      detail: 'Excel dosyası hazırlanıyor...'
    });
  }

  pdfAktar() {
    // PDF export işlemi - şimdilik mockup
    this.messageService.add({
      severity: 'info',
      summary: 'Bilgi',
      detail: 'PDF dosyası hazırlanıyor...'
    });
  }

  kayitlariYenile() {
    this.iletisimTespitleriniYukle();
  }

  durumSeviyesiGetir(durum: string): 'success' | 'info' | 'warn' | 'danger' | 'secondary' | 'contrast' {
    const seviyeler: { [key: string]: 'success' | 'info' | 'warn' | 'danger' | 'secondary' | 'contrast' } = {
      'BEKLEMEDE': 'warn',
      'ISLEMDE': 'info',
      'TAMAMLANDI': 'success',
      'HATA': 'danger'
    };
    return seviyeler[durum] || 'secondary';
  }

  durumMetniGetir(durum: string): string {
    const metinler: { [key: string]: string } = {
      'BEKLEMEDE': 'Beklemede',
      'ISLEMDE': 'İşlemde',
      'TAMAMLANDI': 'Tamamlandı',
      'HATA': 'Hata'
    };
    return metinler[durum] || durum;
  }

  tespitiTuruRengiGetir(tur: string): 'success' | 'info' | 'warn' | 'danger' | 'secondary' | 'contrast' {
    const renkler: { [key: string]: 'success' | 'info' | 'warn' | 'danger' | 'secondary' | 'contrast' } = {
      'GSM_ABONE': 'info',
      'SABIT_HAT': 'success',
      'INTERNET': 'warn',
      'IP_ADRESI': 'secondary',
      'EPOSTA': 'danger'
    };
    return renkler[tur] || 'secondary';
  }

  tespitiTuruMetniGetir(tur: string): string {
    const metinler: { [key: string]: string } = {
      'GSM_ABONE': 'GSM Abone',
      'SABIT_HAT': 'Sabit Hat',
      'INTERNET': 'İnternet',
      'IP_ADRESI': 'IP Adresi',
      'EPOSTA': 'E-posta'
    };
    return metinler[tur] || tur;
  }

  tarihFormatiDuzelt(tarih: Date | string): string {
    if (!tarih) return '-';
    const tarihObj = typeof tarih === 'string' ? new Date(tarih) : tarih;
    return tarihObj.toLocaleDateString('tr-TR') + ' ' + tarihObj.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' });
  }

  ilerlemeDurumunuGetir(durum: string): number {
    const ilerlemeler: { [key: string]: number } = {
      'BEKLEMEDE': 25,
      'ISLEMDE': 50,
      'TAMAMLANDI': 100,
      'HATA': 0
    };
    return ilerlemeler[durum] || 0;
  }

  sonucSayisiniGetir(): { toplam: number, tamamlanan: number, bekleyen: number, hata: number } {
    const toplam = this.filtrelenmissonuclar.length;
    const tamamlanan = this.filtrelenmissonuclar.filter(s => s.durum === 'TAMAMLANDI').length;
    const bekleyen = this.filtrelenmissonuclar.filter(s => s.durum === 'BEKLEMEDE' || s.durum === 'ISLEMDE').length;
    const hata = this.filtrelenmissonuclar.filter(s => s.durum === 'HATA').length;
    
    return { toplam, tamamlanan, bekleyen, hata };
  }
}
