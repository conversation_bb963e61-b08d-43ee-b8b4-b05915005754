<div class="p-4">
  <!-- Başlık -->
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-2xl font-bold text-gray-800">
      <i class="pi pi-code mr-2"></i>
      XML Sorgulama ve Validasyon
    </h2>
    <div class="flex gap-2">
      <p-button 
        icon="pi pi-file" 
        label="Örnekler" 
        severity="info"
        size="small"
        (onClick)="ornekXmlDialogAc()">
      </p-button>
      <p-button 
        icon="pi pi-download" 
        label="İndir" 
        severity="success"
        size="small"
        (onClick)="xmlIndir()"
        [disabled]="!xmlIcerik.trim()">
      </p-button>
    </div>
  </div>

  <!-- Ana <PERSON> -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
    
    <!-- Sol Panel - XML Editörü -->
    <div class="space-y-4">
      
      <!-- <PERSON><PERSON>u -->
      <p-card header="XML Editörü">
        <p-toolbar class="mb-4">
          <div class="p-toolbar-group-start">
            <p-dropdown 
              [options]="xmlSablonlari"
              optionLabel="label"
              placeholder="Şablon Seç"
              (onChange)="sablonSec($event.value)"
              class="mr-2">
            </p-dropdown>
            <p-button 
              icon="pi pi-refresh" 
              severity="secondary"
              size="small"
              (onClick)="xmlTemizle()"
              pTooltip="Temizle"
              tooltipPosition="top">
            </p-button>
          </div>
          <div class="p-toolbar-group-end">
            <p-button 
              icon="pi pi-copy" 
              severity="secondary"
              size="small"
              (onClick)="xmlKopyala()"
              [disabled]="!xmlIcerik.trim()"
              pTooltip="Kopyala"
              tooltipPosition="top"
              class="mr-2">
            </p-button>
            <p-button 
              icon="pi pi-check" 
              label="Validasyon Yap"
              (onClick)="xmlValidasyonYap()"
              [loading]="yukleniyor"
              [disabled]="!xmlIcerik.trim()">
            </p-button>
          </div>
        </p-toolbar>

        <!-- XML Textarea -->
        <div class="xml-editor-container">
          <textarea 
            pInputTextarea 
            [(ngModel)]="xmlIcerik"
            placeholder="XML içeriğinizi buraya yazın veya şablon seçin..."
            rows="20"
            class="w-full font-mono text-sm"
            style="resize: vertical; min-height: 400px;">
          </textarea>
        </div>
      </p-card>

      <!-- Örnek XML Yükleme -->
      <p-card header="Örnek XML Yükle" class="mt-4">
        <div class="flex gap-2">
          <p-dropdown 
            [(ngModel)]="seciliOrnekXml"
            [options]="ornekXmlSecenekleri"
            optionLabel="label"
            optionValue="value"
            placeholder="Örnek XML seçiniz"
            class="flex-1">
          </p-dropdown>
          <p-button 
            icon="pi pi-upload" 
            label="Yükle"
            (onClick)="ornekXmlYukle()"
            [loading]="yukleniyor"
            [disabled]="!seciliOrnekXml">
          </p-button>
        </div>
      </p-card>
    </div>

    <!-- Sağ Panel - Önizleme ve Validasyon -->
    <div class="space-y-4">
      
      <!-- Validasyon Sonucu -->
      <p-card header="Validasyon Sonucu" *ngIf="validasyonYapildi">
        <div *ngIf="validasyonSonucu" class="space-y-3">
          
          <!-- Genel Durum -->
          <div class="flex items-center p-3 rounded" 
               [ngClass]="validasyonSonucu.gecerliMi ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'">
            <i [ngClass]="validasyonSonucu.gecerliMi ? 'pi pi-check-circle text-green-500' : 'pi pi-times-circle text-red-500'" 
               class="text-2xl mr-3"></i>
            <div>
              <p class="font-semibold" 
                 [ngClass]="validasyonSonucu.gecerliMi ? 'text-green-800' : 'text-red-800'">
                {{ validasyonSonucu.gecerliMi ? 'XML Geçerli' : 'XML Geçersiz' }}
              </p>
              <p class="text-sm" 
                 [ngClass]="validasyonSonucu.gecerliMi ? 'text-green-600' : 'text-red-600'">
                {{ validasyonSonucu.gecerliMi ? 'Validasyon başarılı' : 'Hatalar bulundu' }}
              </p>
            </div>
          </div>

          <!-- Hata ve Uyarı Sayıları -->
          <div class="grid grid-cols-2 gap-3">
            <div class="bg-red-50 border border-red-200 rounded p-3 text-center">
              <i class="pi pi-times-circle text-red-500 text-xl mb-1"></i>
              <p class="text-sm text-red-600">Hatalar</p>
              <p class="text-lg font-bold text-red-800">
                {{ validasyonSonucu.hatalar.length || 0 }}
              </p>
            </div>
            <div class="bg-yellow-50 border border-yellow-200 rounded p-3 text-center">
              <i class="pi pi-exclamation-triangle text-yellow-500 text-xl mb-1"></i>
              <p class="text-sm text-yellow-600">Uyarılar</p>
              <p class="text-lg font-bold text-yellow-800">
                {{ validasyonSonucu.uyarilar.length || 0 }}
              </p>
            </div>
          </div>

          <!-- Detay Butonu -->
          <div class="text-center" *ngIf="(validasyonSonucu.hatalar.length || 0) > 0 || (validasyonSonucu.uyarilar.length || 0) > 0">
            <p-button 
              icon="pi pi-eye" 
              label="Detayları Görüntüle"
              severity="info"
              size="small"
              (onClick)="validasyonDetayGoster()">
            </p-button>
          </div>
        </div>
      </p-card>

      <!-- XML Önizleme -->
      <p-card header="XML Önizleme">
        <p-tabView [(activeIndex)]="aktifTabIndex">
          
          <!-- Ham XML -->
          <p-tabPanel header="Ham XML">
            <div class="xml-preview-container">
              <pre class="xml-content">{{ xmlIcerik || 'XML içeriği yok' }}</pre>
            </div>
          </p-tabPanel>

          <!-- Formatlı XML -->
          <p-tabPanel header="Formatlı XML">
            <div class="xml-preview-container">
              <pre class="xml-content formatted">{{ formatliXmlIcerik || xmlFormatiDuzelt(xmlIcerik) || 'XML içeriği yok' }}</pre>
            </div>
          </p-tabPanel>

          <!-- XML Ağacı -->
          <p-tabPanel header="XML Yapısı">
            <div class="xml-tree-container">
              <div *ngIf="!xmlIcerik.trim()" class="text-center py-8 text-gray-500">
                <i class="pi pi-info-circle text-4xl mb-2"></i>
                <p>XML içeriği yok</p>
              </div>
              <div *ngIf="xmlIcerik.trim()" class="text-center py-8 text-gray-500">
                <i class="pi pi-sitemap text-4xl mb-2"></i>
                <p>XML ağaç görünümü yakında eklenecek</p>
              </div>
            </div>
          </p-tabPanel>
        </p-tabView>
      </p-card>

      <!-- Hızlı İstatistikler -->
      <p-card header="İstatistikler" *ngIf="xmlIcerik.trim()">
        <div class="grid grid-cols-2 gap-3">
          <div class="text-center">
            <i class="pi pi-file-o text-blue-500 text-xl mb-1"></i>
            <p class="text-sm text-gray-600">Karakter Sayısı</p>
            <p class="text-lg font-bold text-blue-800">{{ xmlIcerik.length }}</p>
          </div>
          <div class="text-center">
            <i class="pi pi-list text-green-500 text-xl mb-1"></i>
            <p class="text-sm text-gray-600">Satır Sayısı</p>
            <p class="text-lg font-bold text-green-800">{{ xmlIcerik.split('\n').length }}</p>
          </div>
        </div>
      </p-card>
    </div>
  </div>

  <!-- Toast Mesajları -->
  <p-toast></p-toast>
</div>

<!-- Validasyon Detay Dialog -->
<p-dialog 
  header="XML Validasyon Detayları" 
  [(visible)]="validasyonDialogGoruntule"
  [modal]="true"
  [style]="{width: '80vw', maxWidth: '800px'}"
  [closable]="true"
  (onHide)="validasyonDialogKapat()">
  
  <div *ngIf="validasyonSonucu" class="space-y-4">
    
    <!-- Genel Durum -->
    <div class="flex items-center p-3 rounded" 
         [ngClass]="validasyonSonucu.gecerliMi ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'">
      <i [ngClass]="validasyonSonucu.gecerliMi ? 'pi pi-check-circle text-green-500' : 'pi pi-times-circle text-red-500'" 
         class="text-2xl mr-3"></i>
      <div>
        <p class="font-semibold" 
           [ngClass]="validasyonSonucu.gecerliMi ? 'text-green-800' : 'text-red-800'">
          {{ validasyonSonucu.gecerliMi ? 'XML Geçerli' : 'XML Geçersiz' }}
        </p>
        <p class="text-sm" 
           [ngClass]="validasyonSonucu.gecerliMi ? 'text-green-600' : 'text-red-600'">
          {{ validasyonSonucu.gecerliMi ? 'Tüm validasyon kontrolleri başarılı' : 'Aşağıdaki hatalar düzeltilmeli' }}
        </p>
      </div>
    </div>

    <!-- Hatalar -->
    <div *ngIf="validasyonSonucu.hatalar && validasyonSonucu.hatalar.length > 0">
      <h4 class="text-lg font-semibold text-red-800 mb-3">
        <i class="pi pi-times-circle mr-2"></i>
        Hatalar ({{ validasyonSonucu.hatalar.length }})
      </h4>
      <div class="space-y-2 max-h-60 overflow-y-auto">
        <div *ngFor="let hata of validasyonSonucu.hatalar; let i = index" 
             class="bg-red-50 border border-red-200 rounded p-3">
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <p class="text-red-800 font-medium">{{ hata.mesaj }}</p>
              <p class="text-sm text-red-600 mt-1">
                <i class="pi pi-map-marker mr-1"></i>
                Satır: {{ hata.satir }}, Sütun: {{ hata.sutun }}
              </p>
            </div>
            <div class="flex items-center ml-3">
              <p-tag [value]="hata.seviye" severity="danger"></p-tag>
              <span class="ml-2 text-xs text-red-500">#{{ i + 1 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Uyarılar -->
    <div *ngIf="validasyonSonucu.uyarilar && validasyonSonucu.uyarilar.length > 0">
      <h4 class="text-lg font-semibold text-yellow-800 mb-3">
        <i class="pi pi-exclamation-triangle mr-2"></i>
        Uyarılar ({{ validasyonSonucu.uyarilar.length }})
      </h4>
      <div class="space-y-2 max-h-60 overflow-y-auto">
        <div *ngFor="let uyari of validasyonSonucu.uyarilar; let i = index" 
             class="bg-yellow-50 border border-yellow-200 rounded p-3">
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <p class="text-yellow-800 font-medium">{{ uyari.mesaj }}</p>
              <p class="text-sm text-yellow-600 mt-1">
                <i class="pi pi-map-marker mr-1"></i>
                Satır: {{ uyari.satir }}, Sütun: {{ uyari.sutun }}
              </p>
              <p *ngIf="uyari.oneri" class="text-sm text-yellow-700 mt-2 bg-yellow-100 p-2 rounded">
                <i class="pi pi-lightbulb mr-1"></i>
                <strong>Öneri:</strong> {{ uyari.oneri }}
              </p>
            </div>
            <div class="flex items-center ml-3">
              <p-tag [value]="'WARNING'" severity="warn"></p-tag>
              <span class="ml-2 text-xs text-yellow-500">#{{ i + 1 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Başarılı durum -->
    <div *ngIf="validasyonSonucu.gecerliMi && 
                (!validasyonSonucu.hatalar || validasyonSonucu.hatalar.length === 0) &&
                (!validasyonSonucu.uyarilar || validasyonSonucu.uyarilar.length === 0)">
      <div class="text-center py-8">
        <i class="pi pi-check-circle text-6xl text-green-500 mb-4"></i>
        <p class="text-xl text-green-800 font-semibold mb-2">Mükemmel!</p>
        <p class="text-green-600">XML dosyası tamamen geçerli ve herhangi bir sorun bulunmadı.</p>
      </div>
    </div>
  </div>

  <ng-template pTemplate="footer">
    <p-button 
      label="Kapat" 
      icon="pi pi-times" 
      (onClick)="validasyonDialogKapat()"
      severity="secondary">
    </p-button>
  </ng-template>
</p-dialog>

<!-- Örnek XML Dialog -->
<p-dialog 
  header="Örnek XML Şablonları" 
  [(visible)]="ornekXmlDialogGoruntule"
  [modal]="true"
  [style]="{width: '90vw', maxWidth: '1000px'}"
  [closable]="true"
  (onHide)="ornekXmlDialogKapat()">
  
  <div class="space-y-4">
    <p class="text-gray-600">
      Aşağıdaki şablonlardan birini seçerek hızlıca XML oluşturmaya başlayabilirsiniz.
    </p>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div *ngFor="let sablon of xmlSablonlari" 
           class="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
           (click)="sablonSec(sablon); ornekXmlDialogKapat()">
        <div class="flex items-center mb-3">
          <i class="pi pi-file-o text-blue-500 text-xl mr-2"></i>
          <h4 class="font-semibold text-gray-800">{{ sablon.label }}</h4>
        </div>
        <p class="text-sm text-gray-600 mb-3">
          {{ sablon.value === 'bos' ? 'Boş XML şablonu ile başlayın' : 
             sablon.value === 'gsm_dinleme' ? 'GSM dinleme kararı için hazır şablon' :
             'İletişim tespiti için hazır şablon' }}
        </p>
        <div class="text-xs text-gray-500">
          <i class="pi pi-code mr-1"></i>
          {{ sablon.icerik.split('\n').length }} satır
        </div>
      </div>
    </div>
  </div>

  <ng-template pTemplate="footer">
    <p-button 
      label="Kapat" 
      icon="pi pi-times" 
      (onClick)="ornekXmlDialogKapat()"
      severity="secondary">
    </p-button>
  </ng-template>
</p-dialog>
