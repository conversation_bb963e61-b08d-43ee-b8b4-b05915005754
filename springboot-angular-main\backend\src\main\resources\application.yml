server:
  port: 8080

jwt:
  secret: 0123456789abcdef0123456789abcdef
  expiration: 86400000 # 24 saat

spring:
  datasource:
    url: ****************************************
    username: demo_user
    password: demo_password
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    show-sql: true
    format-sql: true

  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true

logging:
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.flywaydb: DEBUG
