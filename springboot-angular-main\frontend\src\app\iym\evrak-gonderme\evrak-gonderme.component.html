<div class="p-4">
  <!-- Başlık -->
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-2xl font-bold text-gray-800">
      <i class="pi pi-upload mr-2"></i>
      E<PERSON><PERSON>
    </h2>
    <div class="flex gap-2">
      <p-button 
        icon="pi pi-refresh" 
        label="Başarılıları Sil" 
        severity="success"
        size="small"
        (onClick)="basariliDosyalariSil()"
        [disabled]="basariliDosyaYok">
      </p-button>
      <p-button 
        icon="pi pi-times" 
        label="Hatalıları Sil" 
        severity="danger"
        size="small"
        (onClick)="hataliDosyalariSil()"
        [disabled]="hataliDosyaYok">
      </p-button>
    </div>
  </div>

  <!-- Dosya Yükleme Alanı -->
  <p-card header="XML Dosya Yükleme" class="mb-4">
    <div class="space-y-4">
      
      <!-- Evrak Tipi Seçimi -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="flex flex-col">
          <label for="evrakTipi" class="text-sm font-medium text-gray-700 mb-1">
            Evrak Tipi
          </label>
          <p-dropdown 
            [(ngModel)]="seciliEvrakTipi"
            [options]="evrakTipleri"
            optionLabel="label"
            optionValue="value"
            placeholder="Evrak tipi seçiniz"
            inputId="evrakTipi"
            class="w-full">
          </p-dropdown>
        </div>
        
        <div class="flex flex-col">
          <label for="aciklama" class="text-sm font-medium text-gray-700 mb-1">
            Açıklama (Opsiyonel)
          </label>
          <textarea 
            pInputTextarea 
            [(ngModel)]="aciklama"
            placeholder="Evrak hakkında açıklama giriniz"
            rows="3"
            class="w-full">
          </textarea>
        </div>
      </div>

      <!-- Dosya Yükleme Komponenti -->
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-6">
        <p-fileUpload 
          name="xmlFiles"
          [multiple]="true"
          accept=".xml"
          [maxFileSize]="maksimumDosyaBoyutu"
          [auto]="false"
          [showUploadButton]="false"
          [showCancelButton]="false"
          (onSelect)="onDosyaSecildi($event)"
          chooseLabel="XML Dosyaları Seç"
          chooseIcon="pi pi-file"
          class="w-full">
          
          <ng-template pTemplate="content">
            <div class="text-center">
              <i class="pi pi-cloud-upload text-4xl text-gray-400 mb-4"></i>
              <p class="text-gray-600 mb-2">XML dosyalarınızı buraya sürükleyin veya seçin</p>
              <p class="text-sm text-gray-500">
                Maksimum dosya boyutu: {{ maksimumDosyaBoyutu / 1024 / 1024 }}MB
              </p>
              <p class="text-sm text-gray-500">
                Kabul edilen format: .xml
              </p>
            </div>
          </ng-template>
        </p-fileUpload>
      </div>

      <!-- Genel İlerleme -->
      <div *ngIf="aktifYukleme" class="space-y-2">
        <div class="flex justify-between items-center">
          <span class="text-sm font-medium text-gray-700">Toplam İlerleme</span>
          <span class="text-sm text-gray-500">{{ toplamIlerleme }}%</span>
        </div>
        <p-progressBar [value]="toplamIlerleme" [showValue]="false"></p-progressBar>
      </div>
    </div>
  </p-card>

  <!-- Yüklenen Dosyalar Listesi -->
  <p-card header="Yüklenen Dosyalar" class="mb-4" *ngIf="yuklenenDosyalar.length > 0">
    <div class="flex justify-between items-center mb-4">
      <span class="text-sm text-gray-600">
        Toplam {{ yuklenenDosyalar.length }} dosya
      </span>
      <div class="flex gap-2">
        <p-button
          icon="pi pi-refresh"
          label="Hatalıları Yeniden Dene"
          severity="warn"
          size="small"
          (onClick)="tumHatalariYenidenDene()"
          [disabled]="hataliDosyaSayisi === 0">
        </p-button>
        <p-button 
          icon="pi pi-trash" 
          label="Tümünü Sil" 
          severity="danger"
          size="small"
          (onClick)="tumDosyalariSil()"
          [disabled]="aktifYukleme">
        </p-button>
      </div>
    </div>

    <p-table 
      [value]="yuklenenDosyalar" 
      responsiveLayout="scroll"
      styleClass="p-datatable-sm">
      
      <ng-template pTemplate="header">
        <tr>
          <th>Dosya Adı</th>
          <th>Boyut</th>
          <th>Durum</th>
          <th>İlerleme</th>
          <th>Validasyon</th>
          <th>İşlemler</th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-dosya let-i="rowIndex">
        <tr>
          <td>
            <div class="flex items-center">
              <i class="pi pi-file text-blue-500 mr-2"></i>
              <span class="font-medium">{{ dosya.dosya.name }}</span>
            </div>
          </td>
          <td>
            <span class="text-sm text-gray-600">
              {{ dosyaBoyutuFormat(dosya.dosya.size) }}
            </span>
          </td>
          <td>
            <p-tag 
              [value]="durumMetniGetir(dosya.durum)"
              [severity]="durumSeviyesiGetir(dosya.durum)">
            </p-tag>
          </td>
          <td>
            <div class="w-24">
              <p-progressBar 
                [value]="dosya.ilerleme" 
                [showValue]="false"
                *ngIf="dosya.durum === 'yukleniyor'">
              </p-progressBar>
              <span *ngIf="dosya.durum !== 'yukleniyor'" class="text-sm text-gray-500">
                {{ dosya.durum === 'basarili' ? '100%' : '-' }}
              </span>
            </div>
          </td>
          <td>
            <div *ngIf="dosya.validasyonSonucu">
              <p-button
                *ngIf="!dosya.validasyonSonucu.gecerliMi"
                icon="pi pi-exclamation-triangle"
                severity="warn"
                size="small"
                (onClick)="validasyonDetayGoster(dosya.validasyonSonucu!)"
                pTooltip="Validasyon Hatalarını Görüntüle"
                tooltipPosition="top">
              </p-button>
              <i *ngIf="dosya.validasyonSonucu.gecerliMi" 
                 class="pi pi-check-circle text-green-500"
                 pTooltip="Validasyon Başarılı"
                 tooltipPosition="top">
              </i>
            </div>
            <span *ngIf="!dosya.validasyonSonucu && dosya.durum === 'basarili'" 
                  class="text-sm text-gray-500">
              Validasyon yok
            </span>
          </td>
          <td>
            <div class="flex gap-1">
              <p-button 
                icon="pi pi-eye" 
                size="small"
                severity="info"
                (onClick)="xmlOnizleme(dosya.dosya)"
                pTooltip="XML Önizleme"
                tooltipPosition="top">
              </p-button>
              <p-button
                *ngIf="dosya.durum === 'hata'"
                icon="pi pi-refresh"
                size="small"
                severity="warn"
                (onClick)="yenidenDene(dosya)"
                pTooltip="Yeniden Dene"
                tooltipPosition="top">
              </p-button>
              <p-button 
                icon="pi pi-trash" 
                size="small"
                severity="danger"
                (onClick)="dosyaSil(i)"
                [disabled]="dosya.durum === 'yukleniyor'"
                pTooltip="Dosyayı Sil"
                tooltipPosition="top">
              </p-button>
            </div>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6" class="text-center py-8">
            <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
            <p class="text-gray-500">Henüz dosya yüklenmedi.</p>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </p-card>

  <!-- Yükleme İstatistikleri -->
  <div *ngIf="yuklenenDosyalar.length > 0" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-center">
        <i class="pi pi-file text-blue-500 text-2xl mr-3"></i>
        <div>
          <p class="text-sm text-blue-600">Toplam Dosya</p>
          <p class="text-xl font-bold text-blue-800">{{ yuklenenDosyalar.length }}</p>
        </div>
      </div>
    </div>
    
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
      <div class="flex items-center">
        <i class="pi pi-check-circle text-green-500 text-2xl mr-3"></i>
        <div>
          <p class="text-sm text-green-600">Başarılı</p>
          <p class="text-xl font-bold text-green-800">
            {{ basariliDosyaSayisi }}
          </p>
        </div>
      </div>
    </div>
    
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex items-center">
        <i class="pi pi-times-circle text-red-500 text-2xl mr-3"></i>
        <div>
          <p class="text-sm text-red-600">Hatalı</p>
          <p class="text-xl font-bold text-red-800">
            {{ hataliDosyaSayisi }}
          </p>
        </div>
      </div>
    </div>
    
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div class="flex items-center">
        <i class="pi pi-clock text-yellow-500 text-2xl mr-3"></i>
        <div>
          <p class="text-sm text-yellow-600">Yükleniyor</p>
          <p class="text-xl font-bold text-yellow-800">
            {{ yuklenenDosyaSayisi }}
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Toast Mesajları -->
  <p-toast></p-toast>
</div>

<!-- Validasyon Detay Dialog -->
<p-dialog 
  header="XML Validasyon Detayları" 
  [(visible)]="validasyonDialogGoruntule"
  [modal]="true"
  [style]="{width: '70vw', maxWidth: '600px'}"
  [closable]="true"
  (onHide)="validasyonDialogKapat()">
  
  <div *ngIf="seciliValidasyonSonucu" class="space-y-4">
    
    <!-- Genel Durum -->
    <div class="flex items-center p-3 rounded" 
         [ngClass]="seciliValidasyonSonucu.gecerliMi ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'">
      <i [ngClass]="seciliValidasyonSonucu.gecerliMi ? 'pi pi-check-circle text-green-500' : 'pi pi-times-circle text-red-500'" 
         class="text-2xl mr-3"></i>
      <div>
        <p class="font-semibold" 
           [ngClass]="seciliValidasyonSonucu.gecerliMi ? 'text-green-800' : 'text-red-800'">
          {{ seciliValidasyonSonucu.gecerliMi ? 'Validasyon Başarılı' : 'Validasyon Başarısız' }}
        </p>
        <p class="text-sm" 
           [ngClass]="seciliValidasyonSonucu.gecerliMi ? 'text-green-600' : 'text-red-600'">
          {{ seciliValidasyonSonucu.gecerliMi ? 'XML dosyası geçerli' : 'XML dosyasında hatalar bulundu' }}
        </p>
      </div>
    </div>

    <!-- Hatalar -->
    <div *ngIf="seciliValidasyonSonucu.hatalar && seciliValidasyonSonucu.hatalar.length > 0">
      <h4 class="text-lg font-semibold text-red-800 mb-2">
        <i class="pi pi-times-circle mr-2"></i>
        Hatalar ({{ seciliValidasyonSonucu.hatalar.length }})
      </h4>
      <div class="space-y-2">
        <div *ngFor="let hata of seciliValidasyonSonucu.hatalar" 
             class="bg-red-50 border border-red-200 rounded p-3">
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <p class="text-red-800 font-medium">{{ hata.mesaj }}</p>
              <p class="text-sm text-red-600">
                Satır: {{ hata.satir }}, Sütun: {{ hata.sutun }}
              </p>
            </div>
            <p-tag [value]="hata.seviye" severity="danger" class="ml-2"></p-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- Uyarılar -->
    <div *ngIf="seciliValidasyonSonucu.uyarilar && seciliValidasyonSonucu.uyarilar.length > 0">
      <h4 class="text-lg font-semibold text-yellow-800 mb-2">
        <i class="pi pi-exclamation-triangle mr-2"></i>
        Uyarılar ({{ seciliValidasyonSonucu.uyarilar.length }})
      </h4>
      <div class="space-y-2">
        <div *ngFor="let uyari of seciliValidasyonSonucu.uyarilar" 
             class="bg-yellow-50 border border-yellow-200 rounded p-3">
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <p class="text-yellow-800 font-medium">{{ uyari.mesaj }}</p>
              <p class="text-sm text-yellow-600">
                Satır: {{ uyari.satir }}, Sütun: {{ uyari.sutun }}
              </p>
              <p *ngIf="uyari.oneri" class="text-sm text-yellow-700 mt-1">
                <i class="pi pi-lightbulb mr-1"></i>
                Öneri: {{ uyari.oneri }}
              </p>
            </div>
            <p-tag [value]="'WARNING'" severity="warn" class="ml-2"></p-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- Başarılı durum mesajı -->
    <div *ngIf="seciliValidasyonSonucu.gecerliMi && 
                (!seciliValidasyonSonucu.hatalar || seciliValidasyonSonucu.hatalar.length === 0) &&
                (!seciliValidasyonSonucu.uyarilar || seciliValidasyonSonucu.uyarilar.length === 0)">
      <div class="text-center py-8">
        <i class="pi pi-check-circle text-6xl text-green-500 mb-4"></i>
        <p class="text-lg text-green-800 font-semibold">XML dosyası tamamen geçerli</p>
        <p class="text-green-600">Herhangi bir hata veya uyarı bulunmadı.</p>
      </div>
    </div>
  </div>

  <ng-template pTemplate="footer">
    <p-button 
      label="Kapat" 
      icon="pi pi-times" 
      (onClick)="validasyonDialogKapat()"
      severity="secondary">
    </p-button>
  </ng-template>
</p-dialog>
