package tr.gov.btk.ulke.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import tr.gov.btk.shared.service.BaseServiceImpl;
import tr.gov.btk.ulke.dto.UlkeDto;
import tr.gov.btk.ulke.entity.Ulke;
import tr.gov.btk.ulke.mapper.UlkeMapper;
import tr.gov.btk.ulke.repository.UlkeRepository;
import tr.gov.btk.ulke.service.UlkeService;
import tr.gov.btk.yetki.dto.YetkiDto;
import tr.gov.btk.yetki.entity.Yetki;
import tr.gov.btk.yetki.mapper.YetkiMapper;
import tr.gov.btk.yetki.repository.YetkiRepository;

@Service

public class UlkeServiceImpl extends BaseServiceImpl<Ulke, UlkeDto, Long> implements UlkeService {

    private final UlkeRepository ulkeRepository;
    private final UlkeMapper ulkeMapper;

    public UlkeServiceImpl(UlkeRepository repository, UlkeMapper mapper) {
        super(repository, mapper);
        this.ulkeRepository = repository;
        this.ulkeMapper = mapper;
    }
    @Override
    public Page<UlkeDto> getPaged(Pageable pageable) {
        return ulkeRepository.findAll(pageable)
                .map(ulkeMapper::toDto);
    }


}
