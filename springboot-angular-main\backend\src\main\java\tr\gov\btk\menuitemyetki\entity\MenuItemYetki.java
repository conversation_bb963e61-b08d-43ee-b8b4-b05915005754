package tr.gov.btk.menuitemyetki.entity;

import jakarta.persistence.*;
import lombok.*;
import tr.gov.btk.menuitem.entity.MenuItem;
import tr.gov.btk.shared.entity.BaseEntity;
import tr.gov.btk.yetki.entity.Yetki;

@Entity
@Table(name = "menu_item_yetkiler")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MenuItemYetki extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "menu_item_id")
    private MenuItem menuItem;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "yetki_id")
    private Yetki yetki;
}
