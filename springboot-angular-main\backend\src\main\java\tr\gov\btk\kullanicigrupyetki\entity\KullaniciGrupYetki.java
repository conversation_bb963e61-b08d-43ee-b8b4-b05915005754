package tr.gov.btk.kullanicigrupyetki.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import tr.gov.btk.kullanicigrup.entity.KullaniciGrup;
import tr.gov.btk.shared.entity.BaseEntity;
import tr.gov.btk.yetki.entity.Yetki;

@Entity
@Table(name = "kullanici_grup_yetkiler")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KullaniciGrupYetki extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "kullanici_grup_id", nullable = false)
    private KullaniciGrup kullaniciGrup;

    @ManyToOne
    @JoinColumn(name = "yetki_id", nullable = false)
    private Yetki yetki;

}
