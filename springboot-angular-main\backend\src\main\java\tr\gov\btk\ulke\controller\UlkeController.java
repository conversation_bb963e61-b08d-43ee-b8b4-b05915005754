package tr.gov.btk.ulke.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tr.gov.btk.ulke.dto.UlkeDto;
import tr.gov.btk.ulke.service.UlkeService;

import java.net.URI;
import java.util.List;

@RestController
@RequestMapping("/api/ulkeler")
@RequiredArgsConstructor
public class UlkeController {

    private final UlkeService ulkeService;

    // ✅ Paging ile listeleme
    @GetMapping
    public Page<UlkeDto> getPagedUlkeler(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        return ulkeService.getPaged(PageRequest.of(page, size));
    }

    // ✅ Tüm kayıtları getirme (opsiyonel)
    @GetMapping("/all")
    public List<UlkeDto> getAllUlkeler() {
        return ulkeService.findAll();
    }

    // ✅ ID ile getirme
    @GetMapping("/{id}")
    public ResponseEntity<UlkeDto> getUlkeById(@PathVariable Long id) {
        UlkeDto dto = ulkeService.findById(id);
        return ResponseEntity.ok(dto);
    }

    // ✅ Yeni ülke ekleme
    @PostMapping
    public ResponseEntity<UlkeDto> createUlke(@RequestBody UlkeDto ulkeDto) {
        UlkeDto created = ulkeService.save(ulkeDto);
        return ResponseEntity.created(URI.create("/api/ulkeler/" + created.getId())).body(created);
    }

    // ✅ Güncelleme
    @PutMapping("/{id}")
    public ResponseEntity<UlkeDto> updateUlke(@PathVariable Long id, @RequestBody UlkeDto ulkeDto) {
        UlkeDto updated = ulkeService.update(id, ulkeDto);
        return ResponseEntity.ok(updated);
    }

    // ✅ Silme
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUlke(@PathVariable Long id) {
        ulkeService.delete(id);
        return ResponseEntity.noContent().build();
    }
}
