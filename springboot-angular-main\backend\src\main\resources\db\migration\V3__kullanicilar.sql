CREATE TABLE kullanici_kullanici_gruplar
(
    id                BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    is_deleted        BOOLEAN,
    created_at        TIMESTAMP WITHOUT TIME ZONE,
    updated_at        TIMESTAMP WITHOUT TIME ZONE,
    deleted_at        TIMESTAMP WITHOUT TIME ZONE,
    created_by        <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by        <PERSON>RC<PERSON><PERSON>(255),
    deleted_by        VARCHAR(255),
    kullanici_id      BIGINT                                  NOT NULL,
    kullanici_grup_id BIGINT                                  NOT NULL,
    CONSTRAINT pk_kullanici_kullanici_gruplar PRIMARY KEY (id)
);

CREATE TABLE kullanicilar
(
    id            BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    is_deleted    BOOLEAN,
    created_at    TIMESTAMP WITHOUT TIME ZONE,
    updated_at    TIMESTAMP WITHOUT TIME ZONE,
    deleted_at    TIMESTAMP WITHOUT TIME ZONE,
    created_by    <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by    <PERSON><PERSON><PERSON><PERSON>(255),
    deleted_by    <PERSON><PERSON><PERSON><PERSON>(255),
    kullanici_adi VARCHAR(255)                            NOT NULL,
    tcno          VARC<PERSON>R(11),
    ad            VARCHAR(255),
    soyad         VARCHAR(255),
    email         VARCHAR(255),
    parola        VARCHAR(255)                            NOT NULL,
    avatar_path   VARCHAR(255),
    status        VARCHAR(255)                            NOT NULL,
    CONSTRAINT pk_kullanicilar PRIMARY KEY (id)
);

ALTER TABLE kullanici_kullanici_gruplar
    ADD CONSTRAINT FK_KULLANICI_KULLANICI_GRUPLAR_ON_KULLANICI FOREIGN KEY (kullanici_id) REFERENCES kullanicilar (id);

ALTER TABLE kullanici_kullanici_gruplar
    ADD CONSTRAINT FK_KULLANICI_KULLANICI_GRUPLAR_ON_KULLANICI_GRUP FOREIGN KEY (kullanici_grup_id) REFERENCES kullanici_gruplar (id);