package iym.common.model.entity.makos;

import iym.common.model.enums.MakosUserAuditType;
import jakarta.persistence.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Entity for MAKOS user audit logging
 * Tracks all user-related operations in the MAKOS module
 */
@Entity(name = "MakosUserAuditLog")
@Table(name = "MAKOS_USER_AUDIT_LOG")
@Data
public class MakosUserAuditLog implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAKOS_USER_AUDIT_LOG_SEQ")
    @SequenceGenerator(name = "MAKOS_USER_AUDIT_LOG_SEQ", sequenceName = "MAKOS_USER_AUDIT_LOG_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "USER_AUDIT_TYPE", nullable = false, length = 100)
    @Enumerated(EnumType.STRING)
    private MakosUserAuditType userAuditType;

    @Column(name = "USERNAME", length = 100)
    private String username;

    @Column(name = "ACTING_USERNAME", length = 100)
    private String actingUsername;

    @Column(name = "USER_IP", length = 100)
    private String userIp;

    @Column(name = "ADMIN_OPERATED_USERNAME", length = 100)
    private String adminOperatedUsername;

    @Column(name = "REQUEST_TIME", nullable = false)
    private LocalDateTime requestTime;

    @Column(name = "RESPONSE_TIME")
    private LocalDateTime responseTime;

    @Column(name = "RESPONSE_CODE")
    private Integer responseCode;

    @PrePersist
    protected void onCreate() {
        if (requestTime == null) {
            requestTime = LocalDateTime.now();
        }
    }
}
