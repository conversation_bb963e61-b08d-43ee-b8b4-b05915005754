// Parametre Sorgulama Component Stilleri

.p-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  
  .p-card-header {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    font-weight: 600;
    border-radius: 8px 8px 0 0;
  }
}

// Tablo stilleri
.p-table {
  .p-datatable-header {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    padding: 1rem;
  }
  
  .p-datatable-thead > tr > th {
    background: #e9ecef;
    color: #495057;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    padding: 0.75rem;
    font-size: 0.875rem;
  }
  
  .p-datatable-tbody > tr {
    transition: background-color 0.2s ease;
    
    &:hover {
      background: #f8f9fa;
    }
    
    &.p-highlight {
      background: #e3f2fd;
    }
  }
  
  .p-datatable-tbody > tr > td {
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem;
    vertical-align: middle;
    font-size: 0.875rem;
  }
}

// Toolbar stilleri
.p-toolbar {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 0.75rem;
  
  .p-toolbar-group-start,
  .p-toolbar-group-end {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .p-input-icon-left {
    position: relative;
    
    input {
      padding-left: 2.5rem;
    }
    
    .pi {
      position: absolute;
      left: 0.75rem;
      top: 50%;
      transform: translateY(-50%);
      color: #6c757d;
    }
  }
}

// Form stilleri
.p-inputtext,
.p-dropdown,
.p-inputtextarea {
  border-radius: 6px;
  border: 1px solid #ced4da;
  transition: all 0.3s ease;
  
  &:focus {
    border-color: #f59e0b;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
    outline: none;
  }
  
  &.ng-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
  }
}

// Tag stilleri
.p-tag {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  
  &.p-tag-success {
    background: #10b981;
    color: white;
  }
  
  &.p-tag-danger {
    background: #ef4444;
    color: white;
  }
  
  &.p-tag-warn {
    background: #f59e0b;
    color: white;
  }
  
  &.p-tag-info {
    background: #3b82f6;
    color: white;
  }
  
  &.p-tag-secondary {
    background: #6b7280;
    color: white;
  }
}

// Buton stilleri
.p-button {
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &.p-button-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  &:hover {
    transform: translateY(-1px);
  }
  
  &.p-button-success {
    background: #10b981;
    border-color: #10b981;
    
    &:hover {
      background: #059669;
      border-color: #059669;
    }
  }
  
  &.p-button-danger {
    background: #ef4444;
    border-color: #ef4444;
    
    &:hover {
      background: #dc2626;
      border-color: #dc2626;
    }
  }
  
  &.p-button-info {
    background: #3b82f6;
    border-color: #3b82f6;
    
    &:hover {
      background: #1d4ed8;
      border-color: #1d4ed8;
    }
  }
  
  &.p-button-secondary {
    background: #6b7280;
    border-color: #6b7280;
    
    &:hover {
      background: #4b5563;
      border-color: #4b5563;
    }
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

// Input Switch stilleri
.p-inputswitch {
  .p-inputswitch-slider {
    background: #e5e7eb;
    border-radius: 1rem;
    transition: all 0.3s ease;
    
    &:before {
      background: white;
      border-radius: 50%;
      transition: all 0.3s ease;
    }
  }
  
  &.p-inputswitch-checked .p-inputswitch-slider {
    background: #10b981;
  }
  
  &:hover .p-inputswitch-slider {
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
  }
}

// Dialog stilleri
.p-dialog {
  .p-dialog-header {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    
    .p-dialog-header-icon {
      color: white;
    }
  }
  
  .p-dialog-content {
    padding: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
  }
  
  .p-dialog-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    background: #f8f9fa;
  }
}

// Checkbox stilleri
.p-checkbox {
  .p-checkbox-box {
    border-radius: 4px;
    border: 2px solid #d1d5db;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #f59e0b;
    }
    
    &.p-highlight {
      background: #f59e0b;
      border-color: #f59e0b;
    }
  }
}

// Parametre değeri gösterimi
.parameter-value {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  background: #f1f3f4;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  color: #1f2937;
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Grup renkleri
.group-badge {
  &.dosya-yukleme {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }
  
  &.xml-islemleri {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  }
  
  &.sistem {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  }
  
  &.guvenlik {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  }
  
  &.performans {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  }
  
  &.loglama {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  }
}

// Bilgi kutusu
.info-box {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #3b82f6;
  border-radius: 6px;
  padding: 1rem;
  
  .info-icon {
    color: #1d4ed8;
    font-size: 1.125rem;
  }
  
  .info-content {
    color: #1e40af;
    
    .info-title {
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
    
    ul {
      margin: 0;
      padding-left: 1rem;
      
      li {
        margin-bottom: 0.25rem;
        line-height: 1.4;
      }
    }
  }
}

// Boş durum gösterimi
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  
  .empty-icon {
    font-size: 3rem;
    color: #9ca3af;
    margin-bottom: 1rem;
  }
  
  .empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 0.5rem;
  }
  
  .empty-description {
    color: #9ca3af;
    font-size: 0.875rem;
  }
}

// Responsive tasarım
@media (max-width: 1024px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .p-toolbar {
    .p-toolbar-group-start,
    .p-toolbar-group-end {
      flex-direction: column;
      align-items: stretch;
      gap: 0.5rem;
    }
    
    .p-input-icon-left input {
      width: 100%;
    }
  }
}

@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }
  
  .p-table {
    font-size: 0.75rem;
    
    .p-datatable-tbody > tr > td {
      padding: 0.5rem;
    }
  }
  
  .p-dialog {
    width: 95vw !important;
    margin: 1rem;
  }
  
  .parameter-value {
    max-width: 120px;
  }
  
  // Mobilde bazı kolonları gizle
  .p-table th:nth-child(5),
  .p-table td:nth-child(5),
  .p-table th:nth-child(7),
  .p-table td:nth-child(7) {
    display: none;
  }
}

// Animasyonlar
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

// Loading durumu
.loading-overlay {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
}

// Scrollbar stilleri
.p-table .p-datatable-wrapper::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.p-table .p-datatable-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.p-table .p-datatable-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.p-table .p-datatable-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
