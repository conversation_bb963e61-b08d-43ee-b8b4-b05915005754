package tr.gov.btk.authentication.controller;

import lombok.RequiredArgsConstructor;

import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.User;
import org.springframework.web.bind.annotation.*;
import tr.gov.btk.authentication.dto.ChangePasswordRequest;
import tr.gov.btk.authentication.dto.JwtResponse;
import tr.gov.btk.authentication.dto.LoginRequest;
import tr.gov.btk.kullanici.entity.Kullanici;
import tr.gov.btk.kullanici.repository.KullaniciRepository;
import tr.gov.btk.kullanici.service.KullaniciService;
import tr.gov.btk.shared.security.JwtService;

@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
public class AuthController {

    private final KullaniciRepository kullaniciRepository;
    private final KullaniciService kullaniciService;
    private final AuthenticationManager authManager;
    private final JwtService jwtTokenProvider;

    @PostMapping("/login")
    public JwtResponse login(@RequestBody LoginRequest request) {
        Authentication authentication = authManager.authenticate(
                new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword()));

        String username = authentication.getName();
        Kullanici kullanici = kullaniciRepository.findByKullaniciAdi(username)
                .orElseThrow(() -> new RuntimeException("Kullanıcı bulunamadı"));

        String token = jwtTokenProvider.generateToken(kullanici, authentication.getAuthorities());

        return new JwtResponse(token,kullanici.getStatus().name());
    }

    @PostMapping("/changePassword")
    public JwtResponse changePassword(@RequestBody ChangePasswordRequest request) {
        return kullaniciService.changePassword(request);
    }
}