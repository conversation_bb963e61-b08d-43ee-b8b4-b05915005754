package iym.db.jpa.dao;

import iym.common.model.entity.iym.HtsHedeflerTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Repository interface for HtsHedeflerTalep entity
 */
@Repository
public interface HtsHedeflerTalepRepo extends JpaRepository<HtsHedeflerTalep, Long> {

    List<HtsHedeflerTalep> findByMahkemeKararId(Long mahkemeKararId);
    
    List<HtsHedeflerTalep> findByHedefNo(String hedefNo);
    
    List<HtsHedeflerTalep> findByHedefNoAndMahkemeKararId(String hedefNo, Long mahkemeKararId);
    
    List<HtsHedeflerTalep> findByKarsiHedefNo(String karsiHedefNo);
    
    List<HtsHedeflerTalep> findByHedefNoAndKarsiHedefNo(String hedefNo, String karsiHedefNo);
    
    List<HtsHedeflerTalep> findBySorguTipi(String sorguTipi);
    
    List<HtsHedeflerTalep> findByTespitTuru(String tespitTuru);
    
    List<HtsHedeflerTalep> findByKullaniciId(String kullaniciId);
    
    List<HtsHedeflerTalep> findByDurumu(String durumu);
    
    List<HtsHedeflerTalep> findByBaslangicTarihiBetween(Date startDate, Date endDate);
    
    List<HtsHedeflerTalep> findByBitisTarihiBetween(Date startDate, Date endDate);
    
    List<HtsHedeflerTalep> findByBaslangicTarihiLessThanEqualAndBitisTarihiGreaterThanEqual(Date date1, Date date2);
    
    List<HtsHedeflerTalep> findByHedefNoContainingIgnoreCase(String hedefNo);
    
    List<HtsHedeflerTalep> findByKarsiHedefNoContainingIgnoreCase(String karsiHedefNo);
    
    List<HtsHedeflerTalep> findBySorguTipiAndTespitTuru(String sorguTipi, String tespitTuru);
    
    List<HtsHedeflerTalep> findByMahkemeKararIdAndDurumu(Long mahkemeKararId, String durumu);
}
