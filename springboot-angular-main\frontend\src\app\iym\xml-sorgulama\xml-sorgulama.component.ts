import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

// PrimeNG Imports
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextarea } from 'primeng/inputtextarea';
import { ToastModule } from 'primeng/toast';
import { DialogModule } from 'primeng/dialog';
import { TabViewModule } from 'primeng/tabview';

import { ScrollPanelModule } from 'primeng/scrollpanel';
import { DividerModule } from 'primeng/divider';
import { TagModule } from 'primeng/tag';
import { MessagesModule } from 'primeng/messages';
import { MessageModule } from 'primeng/message';
import { SplitterModule } from 'primeng/splitter';
import { ToolbarModule } from 'primeng/toolbar';

import { MessageService } from 'primeng/api';

// IYM Imports
import { IymService } from '../shared/services/iym.service';
import { XmlValidasyonSonucu } from '../shared/models/iym.models';

@Component({
  selector: 'app-xml-sorgulama',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CardModule,
    ButtonModule,
    DropdownModule,
    InputTextarea,
    ToastModule,
    DialogModule,
    TabViewModule,
    ScrollPanelModule,
    DividerModule,
    TagModule,
    MessagesModule,
    MessageModule,
    SplitterModule,
    ToolbarModule
  ],
  providers: [MessageService],
  templateUrl: './xml-sorgulama.component.html',
  styleUrls: ['./xml-sorgulama.component.scss']
})
export class XmlSorgulamaComponent implements OnInit {
  
  // XML İçeriği
  xmlIcerik = '';
  formatliXmlIcerik = '';
  
  // Örnek XML'ler
  ornekXmlListesi: string[] = [];
  seciliOrnekXml = '';

  get ornekXmlSecenekleri() {
    return this.ornekXmlListesi.map(o => ({label: o, value: o}));
  }
  
  // Validasyon
  validasyonSonucu: XmlValidasyonSonucu | null = null;
  validasyonYapildi = false;
  
  // UI Durumları
  yukleniyor = false;
  validasyonDialogGoruntule = false;
  ornekXmlDialogGoruntule = false;
  
  // Tab indeksi
  aktifTabIndex = 0;
  
  // XML Şablonları
  xmlSablonlari = [
    {
      label: 'Boş XML Şablonu',
      value: 'bos',
      icerik: `<?xml version="1.0" encoding="ISO-8859-9"?>
<EVRAK_KAYIT>
    <XML_VERSIYON>1</XML_VERSIYON>
    <EVRAK_NO></EVRAK_NO>
    <EVRAK_TARIHI></EVRAK_TARIHI>
    <EVRAK_GELDIGI_KURUM></EVRAK_GELDIGI_KURUM>
    <EVRAK_TIPI></EVRAK_TIPI>
    <HAVALE_BIRIM></HAVALE_BIRIM>
    <GEL_IL></GEL_IL>
    <ACILMI></ACILMI>
    <MAHKEME_KARAR>
        <KARAR_TIP></KARAR_TIP>
        <MAHKEME_KODU></MAHKEME_KODU>
        <MAHKEME_KARAR_NO></MAHKEME_KARAR_NO>
        <MAHKEME_ILI></MAHKEME_ILI>
        <HEDEFLER>
            <HEDEF_NO></HEDEF_NO>
            <HEDEF_TIPI></HEDEF_TIPI>
            <BASLAMA_TARIHI></BASLAMA_TARIHI>
            <SURE_TIPI></SURE_TIPI>
            <BIM_AIDIYAT_KOD></BIM_AIDIYAT_KOD>
        </HEDEFLER>
    </MAHKEME_KARAR>
</EVRAK_KAYIT>`
    },
    {
      label: 'GSM Dinleme Şablonu',
      value: 'gsm_dinleme',
      icerik: `<?xml version="1.0" encoding="ISO-8859-9"?>
<EVRAK_KAYIT>
    <XML_VERSIYON>1</XML_VERSIYON>
    <EVRAK_NO>I.D.B-2024-IG-XXXXX</EVRAK_NO>
    <EVRAK_TARIHI>01/01/2024</EVRAK_TARIHI>
    <EVRAK_GELDIGI_KURUM>02</EVRAK_GELDIGI_KURUM>
    <EVRAK_TIPI>ILETISIMIN_DENETLENMESI</EVRAK_TIPI>
    <HAVALE_BIRIM>02</HAVALE_BIRIM>
    <GEL_IL>0600</GEL_IL>
    <ACILMI>H</ACILMI>
    <MAHKEME_KARAR>
        <KARAR_TIP>100</KARAR_TIP>
        <MAHKEME_KODU>06000204</MAHKEME_KODU>
        <MAHKEME_KARAR_NO>2024/XXXXX</MAHKEME_KARAR_NO>
        <MAHKEME_ILI>3300</MAHKEME_ILI>
        <HEDEFLER>
            <HEDEF_NO>905XXXXXXXX</HEDEF_NO>
            <HEDEF_TIPI>10</HEDEF_TIPI>
            <BASLAMA_TARIHI>01/01/2024</BASLAMA_TARIHI>
            <SURE_TIPI>3</SURE_TIPI>
            <BIM_AIDIYAT_KOD>Y3320001</BIM_AIDIYAT_KOD>
        </HEDEFLER>
    </MAHKEME_KARAR>
</EVRAK_KAYIT>`
    },
    {
      label: 'İletişim Tespiti Şablonu',
      value: 'iletisim_tespiti',
      icerik: `<?xml version="1.0" encoding="ISO-8859-9"?>
<EVRAK_KAYIT>
    <XML_VERSIYON>1</XML_VERSIYON>
    <EVRAK_NO>ADB-2024XXXXXXXXXX</EVRAK_NO>
    <EVRAK_TARIHI>01/01/2024</EVRAK_TARIHI>
    <EVRAK_GELDIGI_KURUM>02</EVRAK_GELDIGI_KURUM>
    <EVRAK_TIPI>ILETISIMIN_TESPITI</EVRAK_TIPI>
    <HAVALE_BIRIM>02</HAVALE_BIRIM>
    <GEL_IL>0600</GEL_IL>
    <ACILMI>H</ACILMI>
    <MAHKEME_KARAR>
        <KARAR_TIP>500</KARAR_TIP>
        <MAHKEME_KODU>06003405</MAHKEME_KODU>
        <MAHKEME_KARAR_NO>2024/XXXXX</MAHKEME_KARAR_NO>
        <MAHKEME_ILI>3300</MAHKEME_ILI>
        <ITK_HEDEFLER>
            <SORGU_TIPI>1</SORGU_TIPI>
            <HEDEF_NO>905XXXXXXXX</HEDEF_NO>
            <BASLANGIC_TARIHI>01/01/2024</BASLANGIC_TARIHI>
            <BITIS_TARIHI>31/01/2024</BITIS_TARIHI>
            <TESPIT_TURU>1</TESPIT_TURU>
        </ITK_HEDEFLER>
    </MAHKEME_KARAR>
</EVRAK_KAYIT>`
    }
  ];

  constructor(
    private iymService: IymService,
    private messageService: MessageService
  ) {}

  ngOnInit() {
    this.ornekXmlListesiniYukle();
  }

  ornekXmlListesiniYukle() {
    this.iymService.xmlOrnekleriGetir().subscribe({
      next: (ornekler) => {
        this.ornekXmlListesi = ornekler;
      },
      error: (hata) => {
        this.messageService.add({
          severity: 'error',
          summary: 'Hata',
          detail: 'Örnek XML listesi yüklenemedi'
        });
      }
    });
  }

  sablonSec(sablon: any) {
    this.xmlIcerik = sablon.icerik;
    this.formatliXmlIcerik = this.xmlFormatiDuzelt(sablon.icerik);
    this.validasyonSonucu = null;
    this.validasyonYapildi = false;
    
    this.messageService.add({
      severity: 'success',
      summary: 'Başarılı',
      detail: `${sablon.label} yüklendi`
    });
  }

  ornekXmlYukle() {
    if (!this.seciliOrnekXml) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Uyarı',
        detail: 'Lütfen bir örnek XML seçiniz'
      });
      return;
    }

    this.yukleniyor = true;
    
    this.iymService.xmlOrnekIcerikGetir(this.seciliOrnekXml).subscribe({
      next: (icerik) => {
        this.xmlIcerik = icerik;
        this.formatliXmlIcerik = this.xmlFormatiDuzelt(icerik);
        this.validasyonSonucu = null;
        this.validasyonYapildi = false;
        this.yukleniyor = false;
        
        this.messageService.add({
          severity: 'success',
          summary: 'Başarılı',
          detail: 'Örnek XML yüklendi'
        });
      },
      error: (hata) => {
        this.yukleniyor = false;
        this.messageService.add({
          severity: 'error',
          summary: 'Hata',
          detail: 'Örnek XML yüklenemedi'
        });
      }
    });
  }

  xmlValidasyonYap() {
    if (!this.xmlIcerik.trim()) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Uyarı',
        detail: 'XML içeriği boş olamaz'
      });
      return;
    }

    this.yukleniyor = true;
    
    this.iymService.xmlValidasyonYap(this.xmlIcerik).subscribe({
      next: (sonuc) => {
        this.validasyonSonucu = sonuc;
        this.validasyonYapildi = true;
        this.yukleniyor = false;
        
        this.messageService.add({
          severity: sonuc.gecerliMi ? 'success' : 'error',
          summary: sonuc.gecerliMi ? 'Başarılı' : 'Hata',
          detail: sonuc.gecerliMi ? 'XML geçerli' : 'XML geçersiz'
        });
      },
      error: (hata) => {
        this.yukleniyor = false;
        this.messageService.add({
          severity: 'error',
          summary: 'Hata',
          detail: 'Validasyon işlemi başarısız oldu'
        });
      }
    });
  }

  xmlFormatiDuzelt(xml: string): string {
    try {
      // Basit XML formatlama
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xml, 'text/xml');
      const serializer = new XMLSerializer();
      let formatted = serializer.serializeToString(xmlDoc);
      
      // Girinti ekleme (basit)
      formatted = formatted.replace(/></g, '>\n<');
      
      return formatted;
    } catch (error) {
      return xml;
    }
  }

  xmlTemizle() {
    this.xmlIcerik = '';
    this.formatliXmlIcerik = '';
    this.validasyonSonucu = null;
    this.validasyonYapildi = false;
    this.seciliOrnekXml = '';
  }

  xmlKopyala() {
    navigator.clipboard.writeText(this.xmlIcerik).then(() => {
      this.messageService.add({
        severity: 'success',
        summary: 'Başarılı',
        detail: 'XML panoya kopyalandı'
      });
    }).catch(() => {
      this.messageService.add({
        severity: 'error',
        summary: 'Hata',
        detail: 'XML kopyalanamadı'
      });
    });
  }

  xmlIndir() {
    const blob = new Blob([this.xmlIcerik], { type: 'text/xml' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'evrak.xml';
    link.click();
    window.URL.revokeObjectURL(url);
    
    this.messageService.add({
      severity: 'success',
      summary: 'Başarılı',
      detail: 'XML dosyası indirildi'
    });
  }

  validasyonDetayGoster() {
    this.validasyonDialogGoruntule = true;
  }

  validasyonDialogKapat() {
    this.validasyonDialogGoruntule = false;
  }

  ornekXmlDialogAc() {
    this.ornekXmlDialogGoruntule = true;
  }

  ornekXmlDialogKapat() {
    this.ornekXmlDialogGoruntule = false;
  }
}
