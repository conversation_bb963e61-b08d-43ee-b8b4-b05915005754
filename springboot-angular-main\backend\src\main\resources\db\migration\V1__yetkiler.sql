CREATE TABLE yetkiler
(
    id         BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    is_deleted BO<PERSON><PERSON><PERSON>,
    created_at TIMESTAMP WITHOUT TIME ZONE,
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    deleted_at TIMESTAMP WITHOUT TIME ZONE,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by VARCHAR(255),
    deleted_by VARC<PERSON><PERSON>(255),
    ad         VARCHAR(255),
    domain     VARCHAR(255),
    CONSTRAINT pk_yetkiler PRIMARY KEY (id)
);