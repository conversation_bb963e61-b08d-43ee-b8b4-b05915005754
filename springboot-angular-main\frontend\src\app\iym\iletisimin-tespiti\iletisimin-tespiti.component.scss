// İleti<PERSON><PERSON>in Tespiti Component Stilleri

.p-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  
  .p-card-header {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    font-weight: 600;
    border-radius: 8px 8px 0 0;
  }
}

// İstatistik kartları
.stats-card {
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  .stats-icon {
    font-size: 2rem;
    margin-right: 1rem;
  }
  
  .stats-label {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
  }
  
  .stats-value {
    font-size: 1.5rem;
    font-weight: 700;
  }
  
  &.blue {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border-color: #3b82f6;
    
    .stats-icon { color: #1d4ed8; }
    .stats-label { color: #1e40af; }
    .stats-value { color: #1e3a8a; }
  }
  
  &.green {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    border-color: #10b981;
    
    .stats-icon { color: #059669; }
    .stats-label { color: #065f46; }
    .stats-value { color: #064e3b; }
  }
  
  &.yellow {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-color: #f59e0b;
    
    .stats-icon { color: #d97706; }
    .stats-label { color: #92400e; }
    .stats-value { color: #78350f; }
  }
  
  &.red {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    border-color: #ef4444;
    
    .stats-icon { color: #dc2626; }
    .stats-label { color: #991b1b; }
    .stats-value { color: #7f1d1d; }
  }
}

// Tablo stilleri
.p-table {
  .p-datatable-header {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    padding: 1rem;
  }
  
  .p-datatable-thead > tr > th {
    background: #e9ecef;
    color: #495057;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    padding: 0.75rem;
    font-size: 0.875rem;
  }
  
  .p-datatable-tbody > tr {
    transition: background-color 0.2s ease;
    
    &:hover {
      background: #f8f9fa;
    }
  }
  
  .p-datatable-tbody > tr > td {
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem;
    vertical-align: middle;
    font-size: 0.875rem;
  }
}

// Toolbar stilleri
.p-toolbar {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 0.75rem;
  
  .p-toolbar-group-start,
  .p-toolbar-group-end {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .p-input-icon-left {
    position: relative;
    
    input {
      padding-left: 2.5rem;
    }
    
    .pi {
      position: absolute;
      left: 0.75rem;
      top: 50%;
      transform: translateY(-50%);
      color: #6c757d;
    }
  }
}

// Form stilleri
.p-inputtext,
.p-dropdown,
.p-calendar {
  border-radius: 6px;
  border: 1px solid #ced4da;
  transition: all 0.3s ease;
  
  &:focus {
    border-color: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
    outline: none;
  }
}

// Tag stilleri
.p-tag {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  
  &.p-tag-success {
    background: #10b981;
    color: white;
  }
  
  &.p-tag-danger {
    background: #ef4444;
    color: white;
  }
  
  &.p-tag-warn {
    background: #f59e0b;
    color: white;
  }
  
  &.p-tag-info {
    background: #3b82f6;
    color: white;
  }
  
  &.p-tag-secondary {
    background: #6b7280;
    color: white;
  }
}

// Progress Bar stilleri
.p-progressbar {
  border-radius: 4px;
  background: #e5e7eb;
  
  .p-progressbar-value {
    border-radius: 4px;
    transition: width 0.3s ease;
    
    &.p-progressbar-value-animate {
      background: linear-gradient(90deg, #10b981 0%, #059669 100%);
    }
  }
  
  &.progress-sm {
    height: 8px;
    
    .p-progressbar-value {
      height: 8px;
    }
  }
  
  &.progress-md {
    height: 12px;
    
    .p-progressbar-value {
      height: 12px;
    }
  }
}

// Buton stilleri
.p-button {
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &.p-button-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  &:hover {
    transform: translateY(-1px);
  }
  
  &.p-button-success {
    background: #10b981;
    border-color: #10b981;
    
    &:hover {
      background: #059669;
      border-color: #059669;
    }
  }
  
  &.p-button-danger {
    background: #ef4444;
    border-color: #ef4444;
    
    &:hover {
      background: #dc2626;
      border-color: #dc2626;
    }
  }
  
  &.p-button-info {
    background: #3b82f6;
    border-color: #3b82f6;
    
    &:hover {
      background: #1d4ed8;
      border-color: #1d4ed8;
    }
  }
  
  &.p-button-secondary {
    background: #6b7280;
    border-color: #6b7280;
    
    &:hover {
      background: #4b5563;
      border-color: #4b5563;
    }
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

// Dialog stilleri
.p-dialog {
  .p-dialog-header {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    
    .p-dialog-header-icon {
      color: white;
    }
  }
  
  .p-dialog-content {
    padding: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
  }
  
  .p-dialog-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    background: #f8f9fa;
  }
}

// Hedef bilgisi gösterimi
.target-info {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  background: #f1f3f4;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  color: #1f2937;
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Detay dialog bölümleri
.detail-section {
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  
  .section-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    
    .pi {
      margin-right: 0.5rem;
    }
  }
  
  .section-content {
    .field-group {
      margin-bottom: 1rem;
      
      .field-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6b7280;
        margin-bottom: 0.25rem;
      }
      
      .field-value {
        font-size: 0.875rem;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
  
  &.general-info {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    
    .section-title {
      color: #374151;
    }
  }
  
  &.status-info {
    background: #eff6ff;
    border: 1px solid #3b82f6;
    
    .section-title {
      color: #1e40af;
    }
    
    .field-label {
      color: #1e40af;
    }
    
    .field-value {
      color: #1e3a8a;
    }
  }
  
  &.date-info {
    background: #ecfdf5;
    border: 1px solid #10b981;
    
    .section-title {
      color: #065f46;
    }
    
    .field-label {
      color: #065f46;
    }
    
    .field-value {
      color: #064e3b;
    }
  }
  
  &.result-info {
    background: #fffbeb;
    border: 1px solid #f59e0b;
    
    .section-title {
      color: #92400e;
    }
    
    .field-value {
      color: #78350f;
      white-space: pre-wrap;
      line-height: 1.5;
    }
  }
  
  &.error-info {
    background: #fef2f2;
    border: 1px solid #ef4444;
    
    .section-title {
      color: #991b1b;
    }
    
    .field-value {
      color: #7f1d1d;
    }
  }
}

// Boş durum gösterimi
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  
  .empty-icon {
    font-size: 3rem;
    color: #9ca3af;
    margin-bottom: 1rem;
  }
  
  .empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 0.5rem;
  }
  
  .empty-description {
    color: #9ca3af;
    font-size: 0.875rem;
  }
}

// Responsive tasarım
@media (max-width: 1024px) {
  .grid {
    &.grid-cols-6 {
      grid-template-columns: repeat(3, 1fr);
    }
    
    &.grid-cols-4 {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  .p-toolbar {
    .p-toolbar-group-start,
    .p-toolbar-group-end {
      flex-direction: column;
      align-items: stretch;
      gap: 0.5rem;
    }
    
    .p-input-icon-left input {
      width: 100%;
    }
  }
}

@media (max-width: 768px) {
  .grid {
    &.grid-cols-6,
    &.grid-cols-4,
    &.grid-cols-3 {
      grid-template-columns: 1fr;
    }
  }
  
  .p-table {
    font-size: 0.75rem;
    
    .p-datatable-tbody > tr > td {
      padding: 0.5rem;
    }
  }
  
  .p-dialog {
    width: 95vw !important;
    margin: 1rem;
  }
  
  .target-info {
    max-width: 120px;
  }
  
  // Mobilde bazı kolonları gizle
  .p-table th:nth-child(2),
  .p-table td:nth-child(2),
  .p-table th:nth-child(6),
  .p-table td:nth-child(6) {
    display: none;
  }
  
  .stats-card {
    padding: 1rem;
    
    .stats-icon {
      font-size: 1.5rem;
    }
    
    .stats-value {
      font-size: 1.25rem;
    }
  }
}

// Animasyonlar
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

// Loading durumu
.loading-overlay {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
}

// Scrollbar stilleri
.p-table .p-datatable-wrapper::-webkit-scrollbar,
.p-dialog-content::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.p-table .p-datatable-wrapper::-webkit-scrollbar-track,
.p-dialog-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.p-table .p-datatable-wrapper::-webkit-scrollbar-thumb,
.p-dialog-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.p-table .p-datatable-wrapper::-webkit-scrollbar-thumb:hover,
.p-dialog-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
